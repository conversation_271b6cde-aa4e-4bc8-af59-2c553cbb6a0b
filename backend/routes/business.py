# routes/employer.py
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from datetime import datetime

from auth.business import <PERSON><PERSON>uth
from supabase import create_client
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials

router = APIRouter()

# Pydantic models for request validation
class CreateShiftRequest(BaseModel):
    title: str
    description: str
    start_time: str  # ISO format datetime string
    end_time: str    # ISO format datetime string
    hourly_rate: float
    currency: str = "EUR"
    venue_id: int

# Init Supabase client
supabase = create_client("https://ojggxxcgrxtnxzbgvtyl.supabase.co", "sb_secret_JyGmpaohI9zlYCX5J9y5WA_V2PnjOWo")
security = HTTPBearer()

@router.get("/verify_access")
async def employer_access(user=Depends(BusinessAuth())):
    return {"message": "Bienvenue pro", "auth": True}

@router.get("/get_companies")
async def get_companies(user=Depends(BusinessAuth())):
    companies = get_company(user.id)
    return {
        "companies": companies
    }


@router.get("/get_lieux")
async def get_lieux(company_id: int, user=Depends(BusinessAuth())):
    
    lieux_resp = (
        supabase
        .table("venues")
        .select("id, name, logo")
        .eq("company", company_id)
        .execute()
    )
    lieux = lieux_resp.data or []
    return {"lieux": lieux}


@router.get("/get_lieux_info")
async def get_lieux_info(venue_id: int, user=Depends(BusinessAuth())):
    
    lieu_resp = (
        supabase
        .table("venues")
        .select("id, name, logo, type, cuisine, email, phone_number, adresse, postal_code, town, country, created_at")
        .eq("id", venue_id)
        .execute()
    )
    lieu = lieu_resp.data[0] or {}

    print("lieu : ", lieu)
    return lieu

@router.get("/get_shifts_by_venue")
async def get_shifts_by_venue(venue_id: int, user=Depends(BusinessAuth())):
    
    shifts_resp = (
        supabase
        .table("shifts")
        .select("id, title, description, start_time, end_time, hourly_rate, currency, status")
        .eq("venue", venue_id)
        .execute()
    )
    shifts = shifts_resp.data or []
    return shifts

@router.post("/create_shift")
async def create_shift(shift_data: CreateShiftRequest, user=Depends(BusinessAuth())):
    try:
        # Validate datetime strings
        start_datetime = datetime.fromisoformat(shift_data.start_time.replace('Z', '+00:00'))
        end_datetime = datetime.fromisoformat(shift_data.end_time.replace('Z', '+00:00'))

        if end_datetime <= start_datetime:
            raise HTTPException(status_code=400, detail="End time must be after start time")

        # Create the shift in the database
        shift_resp = (
            supabase
            .table("shifts")
            .insert({
                "title": shift_data.title,
                "description": shift_data.description,
                "start_time": shift_data.start_time,
                "end_time": shift_data.end_time,
                "hourly_rate": shift_data.hourly_rate,
                "currency": shift_data.currency,
                "venue": shift_data.venue_id,
                "created_at": datetime.now().isoformat(),
                "status": "published"
            })
            .execute()
        )

        if not shift_resp.data:
            raise HTTPException(status_code=500, detail="Failed to create shift")

        return {
            "message": "Shift created successfully",
            "shift": shift_resp.data[0]
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid datetime format: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating shift: {str(e)}")

class PublishShiftRequest(BaseModel):
    shift_id: int

@router.post("/publish_shift")
async def publish_shift(req: PublishShiftRequest, user=Depends(BusinessAuth())):
    try:
        shift_resp = (
            supabase
            .table("shifts")
            .update({"status": "published"})
            .eq("id", req.shift_id)
            .execute()
        )
        if not shift_resp.data:
            raise HTTPException(status_code=500, detail="Failed to publish shift")

        return {
            "message": "Shift published successfully",
            "shift": shift_resp.data[0]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error publishing shift: {str(e)}")

@router.post("/cancel_shift")
async def cancel_shift(req: PublishShiftRequest, user=Depends(BusinessAuth())):
    try:
        shift_resp = (
            supabase
            .table("shifts")
            .update({"status": "cancelled"})
            .eq("id", req.shift_id)
            .execute()
        )
        if not shift_resp.data:
            raise HTTPException(status_code=500, detail="Failed to cancel shift")

        return {
            "message": "Shift cancelled successfully",
            "shift": shift_resp.data[0]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error cancelling shift: {str(e)}")

@router.get("/get_venue_workers")
async def get_venue_workers(venue_id: int, user=Depends(BusinessAuth())):
    # Étape 1 : Récupérer les IDs des workers liés à ce venue
    workers_links_resp = (
        supabase
        .table("venue_pool_workers")
        .select("worker")
        .eq("venue", venue_id)
        .execute()
    )

    worker_ids = [row["worker"] for row in (workers_links_resp.data or [])]
    print("worker_ids : ", worker_ids)
    if not worker_ids:
        return []

    # Étape 2 : Récupérer les infos des workers
    workers_resp = (
        supabase
        .table("worker_infos")
        .select("id, first_name, last_name, experience_level, profile_picture")
        .in_("id", worker_ids)
        .execute()
    )

    return workers_resp.data or []


@router.get("/get_worker_info")
async def get_worker_info(worker_id: int, user=Depends(BusinessAuth())):
    res = (
        supabase
        .table("worker_infos")
        .select("*")
        .eq("id", worker_id)
        .limit(1)
        .execute()
    )
    if not res.data:
        raise HTTPException(status_code=404, detail="Worker not found")
    return res.data[0]


@router.get("/get_shift_applications")
async def get_shift_applications(shift_id: int, user=Depends(BusinessAuth())):

    res = (
        supabase
        .table("shift_applications")
        .select("id, worker(id, first_name, last_name), status")
        .eq("shift", shift_id)
        .execute()
    )
    return res.data or []

@router.get("/get_companies")
async def employer_dashboard(user=Depends(BusinessAuth())):
    
    # # try:
    employer_query = (
        supabase
        .table("employers")
        .select("id")
        .eq("user", user.id)
        .limit(1)
        .execute()
    )

    print("employers id : ", employer_query)

    # query = (
    #     supabase
    #     .table("companies_membership")
    #     .select("companies:company(*)")  # jointure vers la table companies
    #     .eq("employer", employer_query)        # ou user.id selon ton objet
    # )

    # response = query.execute()
    # print("response : ", response)
    # companies = [item["name"] for item in response.data]
    # print('query : ', companies)
    # # except:
    # #     raise HTTPException(status_code=403, detail="Access denied")
    
    return {"message": "entreprises", "user": user}
