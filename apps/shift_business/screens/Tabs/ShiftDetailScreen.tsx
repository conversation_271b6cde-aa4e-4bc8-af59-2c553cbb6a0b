import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, ActivityIndicator, Button } from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { BACKEND_URL } from '../../constants/env';

interface Shift {
  id: number;
  title: string;
  description: string;
  start_time: string;
  end_time: string;
  hourly_rate: string;
  currency: string;
  status: string;
}

interface Application {
  id: number;
  worker_id: number;
  worker_first_name: string;
  worker_last_name: string;
  status: string;
}

type ShiftDetailRouteProp = RouteProp<{ ShiftDetail: { shift: Shift } }, 'ShiftDetail'>;

export default function ShiftDetailScreen() {
  const route = useRoute<ShiftDetailRouteProp>();
  const { shift } = route.params;

  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchApplications = async () => {
      try {
        const token = await AsyncStorage.getItem('supabase_jwt_token');

        const response = await fetch(`${BACKEND_URL}/get_shift_applications?shift_id=${shift.id}`, {
          headers: { Authorization: `Bearer ${token}` },
        });

        if (response.ok) {
          const data = await response.json();
          setApplications(data);
        } else {
          console.error('Erreur lors du chargement des candidatures');
        }
      } catch (err) {
        console.error('Erreur de requête :', err);
      } finally {
        setLoading(false);
      }
    };

    if (shift.status === 'published') {
        fetchApplications();
    } else {
        setLoading(false);
    }
  }, [shift.id]);

    async function publishShift() {
        try {
            const token = await AsyncStorage.getItem('supabase_jwt_token');
            console.log("shift id : ", shift.id);
            const response = await fetch(`${BACKEND_URL}/publish_shift`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify({ shift_id: shift.id }),
            });

            if (response.ok) {
                alert('Shift published successfully!');
                // Optionally, you can update the shift status here or refetch the data
            } else {
                console.error('Failed to publish shift');
                alert('Failed to publish shift. Please try again.');
            }
        } catch (err) {
            console.error('Error publishing shift:', err);
            alert('An error occurred while publishing the shift.');
        }
    }
    async function CancelShift() {
        try {
            const token = await AsyncStorage.getItem('supabase_jwt_token');
            const response = await fetch(`${BACKEND_URL}/cancel_shift`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify({ shift_id: shift.id }),
            });

            if (response.ok) {
                alert('Shift canceled successfully!');
                // Optionally, you can update the shift status here or refetch the data
            } else {
                console.error('Failed to cancel shift');
                alert('Failed to cancel shift. Please try again.');
            }
        } catch (err) {
            console.error('Error canceling shift:', err);
            alert('An error occurred while canceling the shift.');
        }
    }
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.card}>
        <Text style={styles.title}>{shift.title}</Text>

        <View style={styles.section}>
          <Text style={styles.label}>📝 Description</Text>
          <Text style={styles.text}>{shift.description}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>🕒 Horaires</Text>
          <Text style={styles.text}>
            {new Date(shift.start_time).toLocaleString()} — {new Date(shift.end_time).toLocaleString()}
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>💰 Rémunération</Text>
          <Text style={styles.text}>
            {shift.hourly_rate} {shift.currency}/h
          </Text>
        </View>

        <View style={styles.section}>
          {shift.status === 'published' ? (
                <>
                    <Text style={styles.label}>👥 Candidatures</Text>
                    {loading ? (
                        <ActivityIndicator size="small" color="#666" />
                    ) : (
                        <>
                            <Text style={styles.text}>{applications.length} candidature(s)</Text>
                            {applications.map((app) => (
                                <Text key={app.id} style={styles.text}>
                                {app.worker_first_name} {app.worker_last_name} — {app.status}
                                </Text>
                            ))}
                        </>
                    )}
                    <Button title="Cancel" onPress={() => CancelShift()} />
                </>
            ) : (
                <>
                    <Text style={styles.text}>Statut: {shift.status}</Text>
                    <Button title="Publier" onPress={() => publishShift()} />
                </>
                
            )
        }
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f2f2f7',
    padding: 20,
  },
  card: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 10,
    shadowOffset: { width: 0, height: 4 },
    elevation: 4,
  },
  title: {
    fontSize: 26,
    fontWeight: '700',
    marginBottom: 20,
    color: '#333',
  },
  section: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 6,
    color: '#555',
  },
  text: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
  },
});
